{
  "name": "CertRats Development Environment",
  "image": "mcr.microsoft.com/devcontainers/python:3.11-bullseye",
  "workspaceFolder": "/workspace",

  // Minimal features
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "20"
    },
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "enableNonRootDocker": true
    }
  },

  // Basic environment variables
  "containerEnv": {
    "PYTHONPATH": "/workspace",
    "NODE_ENV": "development"
  },

  // VS Code customizations
  "customizations": {
    "vscode": {
      "settings": {
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "editor.formatOnSave": true,
        "files.exclude": {
          "**/__pycache__": true,
          "**/*.pyc": true,
          "**/node_modules": true
        }
      },

      "extensions": [
        // Essential development
        "ms-python.python",
        "ms-vscode.vscode-json",
        "ms-azuretools.vscode-docker",

        // Augment extension
        "augmentcode.augment",

        // Mermaid diagrams
        "bierner.markdown-mermaid",

        // Basic productivity
        "yzhang.markdown-all-in-one",
        "editorconfig.editorconfig"
      ]
    }
  },

  // Basic port forwarding
  "forwardPorts": [3000, 8000],

  // Mount project folder to workspace
  "mounts": [
    "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached"
  ],

  // Simple post-creation setup
  "postCreateCommand": "bash .devcontainer/setup.sh",

  // User settings
  "remoteUser": "vscode"
}
